import React from 'react';

interface StatusIndicatorProps {
  status: 'sent' | 'delivered' | 'failed' | 'pending';
}

const StatusIndicator: React.FC<StatusIndicatorProps> = ({ status }) => {
  const statusConfig = {
    sent: { color: 'bg-blue-500', text: 'Sent' },
    delivered: { color: 'bg-green-500', text: 'Delivered' },
    failed: { color: 'bg-red-500', text: 'Failed' },
    pending: { color: 'bg-yellow-500', text: 'Pending' },
  };

  const config = statusConfig[status];

  return (
    <div className="flex items-center gap-2">
      <div className={`w-2 h-2 rounded-full ${config.color}`} />
      <span className="text-sm text-gray-600">{config.text}</span>
    </div>
  );
};

export default StatusIndicator;
