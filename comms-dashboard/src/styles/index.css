@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@layer base {
  body {
    @apply bg-white text-gray-800 font-['Inter'];
  }
}

@layer components {
  .btn-primary {
    @apply px-4 py-2 bg-gradient-to-b from-blue-600 to-blue-700 text-white rounded-md
    hover:scale-102 transition-all duration-200 hover:shadow-md;
  }

  .card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm p-4
    hover:shadow-md transition-all duration-200;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md
    focus:ring-2 focus:ring-blue-500 focus:border-transparent
    transition-all duration-200;
  }

  .sidebar-link {
    @apply flex items-center gap-2 px-4 py-2 text-gray-600
    hover:bg-blue-50 hover:text-blue-600 rounded-md
    transition-all duration-200;
  }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
