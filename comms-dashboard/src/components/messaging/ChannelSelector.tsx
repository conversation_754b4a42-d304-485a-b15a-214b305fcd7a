import React from 'react';
import { MessageChannel } from '../../types/message';

interface ChannelSelectorProps {
  selectedChannel: MessageChannel;
  onChannelChange: (channel: MessageChannel) => void;
}

const ChannelSelector: React.FC<ChannelSelectorProps> = ({
  selectedChannel,
  onChannelChange,
}) => {
  const channels: { value: MessageChannel; label: string; icon: string }[] = [
    { value: 'call', label: 'Call', icon: '📞' },
    { value: 'text', label: 'Text', icon: '💬' },
    { value: 'email', label: 'Email', icon: '📧' },
    { value: 'voicemail', label: 'Voicemail', icon: '🔊' },
  ];

  return (
    <div className="flex gap-2">
      {channels.map((channel) => (
        <button
          key={channel.value}
          onClick={() => onChannelChange(channel.value)}
          className={`px-4 py-2 rounded-md transition-all duration-200 flex items-center gap-2
            ${selectedChannel === channel.value
              ? 'bg-blue-100 text-blue-700'
              : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
            }`}
        >
          <span>{channel.icon}</span>
          <span>{channel.label}</span>
        </button>
      ))}
    </div>
  );
};

export default ChannelSelector;
