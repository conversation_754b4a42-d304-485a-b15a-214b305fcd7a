version: '3'

#################
# SERVICES
#################
services:
  # Developer environment
  devenv:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      # Mount the root folder that contains .git
      - ..:/workspace:cached
    command: /bin/sh -c "while sleep 10; do :; done"
    networks:
      pentestgpt:
        ipv4_address: ***********
    cap_add:
      - NET_ADMIN
      # - ALL

  # OpenSSH vulnerable machine (CVE-2018-15473)
  openssh:
    container_name: openssh 
    build:
      context: ..
      dockerfile: .devcontainer/targets/openssh/Dockerfile
    environment: 
      - ROOT_PASSWORD=vulhub
    # ports:  # map port in the container to the host system
    #   - "20022:22"    
    networks:
      pentestgpt:
        ipv4_address: ************

  # Vulnhub vulnerable machine (Hackable II)
  hackableii:
    container_name: hackableii
    image: vmayoral/vulnhub:hackableii
    command: |
      /bin/bash -c "rm -r /var/lock; mkdir -p /var/lock; chmod 755 /var/lock; /etc/init.d/apache2 start; /etc/init.d/ssh start; /etc/init.d/runproftpd.sh; /etc/init.d/php7.0-fpm start; while sleep 10; do :; done"
    ports:  # map port in the container to the host system
      - "80:80"
    networks:
      pentestgpt:
        ipv4_address: ************
    mac_address: 08:00:27:85:55:86


  # Vulnhub vulnerable machine (Bob)
  bob:
    container_name: bob
    image: vmayoral/vulnhub:bob
    command: |
      /bin/bash -c "rm -r /var/lock; mkdir -p /var/lock; chmod 755 /var/lock; /etc/init.d/apache2 start; /etc/init.d/ssh start; while sleep 10; do :; done"
    ports:  # map port in the container to the host system
      - "8080:80"
    networks:
      pentestgpt:
        ipv4_address: ************
    mac_address: 08:00:27:cb:07:d4


#################
# NETWORKS
#################
networks:
  pentestgpt:
    ipam:
      driver: default
      config:
        - subnet: ***********/24