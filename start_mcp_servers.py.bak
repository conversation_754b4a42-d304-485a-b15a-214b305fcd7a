#!/usr/bin/env python3
"""
MCP Server Activator

This module provides functionality to activate all required MCP servers,
including the local models MCP server for Qwen, Grok, and Sonnet 3.7 models.
"""

import os
import sys
import json
import asyncio
import logging
import subprocess
import signal
from pathlib import Path
import time
from typing import Dict, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("mcp_server_startup.log")
    ]
)
logger = logging.getLogger("mcp-server-activator")

class MCPServerActivator:
    """Manages activation and monitoring of all MCP servers"""
    
    def __init__(self):
        self.script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        self.mcp_registry_path = self.script_dir / "mcp_registry.json"
        self.server_processes = {}
        self.active_servers = set()
        self.server_configs = {}
        self.server_types = {
            "local": {
                "script": "local_models_mcp_server.py",
                "default_port": 8085
            }
        }
        
    async def activate_all_servers(self):
        """Activate all configured MCP servers"""
        logger.info("Activating all MCP servers")
        
        # Load MCP registry
        if self.mcp_registry_path.exists():
            try:
                with open(self.mcp_registry_path) as f:
                    registry_data = json.load(f)
                    
                self.server_configs = registry_data.get("servers", {})
                logger.info(f"Loaded {len(self.server_configs)} servers from registry")
                
            except Exception as e:
                logger.error(f"Error loading registry: {e}")
                self.server_configs = {}
        
        # Activate local models server
        await self.activate_local_models_server()
        
        # Check status of all servers
        await self.check_all_servers()
        
        logger.info(f"Activated {len(self.active_servers)} MCP servers")
        return len(self.active_servers) > 0
    
    async def activate_local_models_server(self):
        """Activate the Local Models MCP Server"""
        server_script = self.script_dir / "local_models_mcp_server.py"
        
        if not server_script.exists():
            logger.error(f"Local Models MCP Server script not found: {server_script}")
            return False
            
        try:
            logger.info("Starting Local Models MCP Server")
            
            # Check if server is already running
            port = 8085
            if await self._is_port_in_use(port):
                logger.info(f"Port {port} is already in use, assuming server is already running")
                return True
                
            # Start the server process
            server_proc = subprocess.Popen(
                [sys.executable, str(server_script), "--port", str(port)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                preexec_fn=os.setsid  # To make the process a process group leader
            )
            
            server_id = f"local-models-mcp"
            self.server_processes[server_id] = server_proc
            
            # Give it a moment to start
            await asyncio.sleep(2)
            
            # Check if it's running
            if server_proc.poll() is None:
                logger.info(f"Local Models MCP Server started successfully on port {port}")
                return True
            else:
                stdout, stderr = server_proc.communicate()
                logger.error(f"Local Models MCP Server failed to start: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error starting Local Models MCP Server: {e}")
            return False
    
    async def check_all_servers(self):
        """Check status of all MCP servers"""
        logger.info("Checking status of all MCP servers")
        
        self.active_servers = set()
        
        # Check registered servers from registry
        for server_id, config in self.server_configs.items():
            url = config.get("url")
            if not url:
                continue
            
            is_active = await self._check_server_health(url)
            if is_active:
                self.active_servers.add(server_id)
                
        # Check local models server
        local_server_url = "http://localhost:8085"
        is_active = await self._check_server_health(local_server_url)
        if is_active:
            self.active_servers.add("local-models-mcp")
            
            # Add to registry if not already present
            if "local-models-mcp" not in self.server_configs:
                self.server_configs["local-models-mcp"] = {
                    "id": "local-models-mcp",
                    "name": "Local Models MCP Server",
                    "url": local_server_url,
                    "registered_at": time.strftime("%Y-%m-%dT%H:%M:%S.%fZ")
                }
                
                # Update registry file
                try:
                    registry_data = {}
                    if self.mcp_registry_path.exists():
                        with open(self.mcp_registry_path) as f:
                            registry_data = json.load(f)
                    
                    registry_data["servers"] = registry_data.get("servers", {})
                    registry_data["servers"]["local-models-mcp"] = self.server_configs["local-models-mcp"]
                    
                    registry_data["capabilities"] = registry_data.get("capabilities", {})
                    registry_data["capabilities"]["local-models-mcp"] = [
                        "text-generation", "code-completion", "reasoning", "tool-use"
                    ]
                    
                    registry_data["active_servers"] = list(set(registry_data.get("active_servers", [])) | {"local-models-mcp"})
                    
                    with open(self.mcp_registry_path, "w") as f:
                        json.dump(registry_data, f, indent=2)
                        
                    logger.info("Added local models MCP server to registry")
                    
                except Exception as e:
                    logger.error(f"Error updating registry: {e}")
        
        return self.active_servers
    
    async def _check_server_health(self, url: str) -> bool:
        """Check health of a server at the given URL"""
        try:
            import aiohttp
            
            health_url = f"{url.rstrip('/')}/health"
            async with aiohttp.ClientSession() as session:
                async with session.get(health_url, timeout=5) as response:
                    if response.status == 200:
                        logger.info(f"Server at {url} is healthy")
                        return True
                    else:
                        logger.warning(f"Server at {url} returned status {response.status}")
                        return False
                        
        except Exception as e:
            logger.warning(f"Error checking server health at {url}: {e}")
            return False
    
    async def _is_port_in_use(self, port: int) -> bool:
        """Check if a port is in use"""
        import socket
        
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("localhost", port))
                return False
        except socket.error:
            return True
    
    def print_server_status(self):
        """Print status of all MCP servers"""
        print("\nMCP Server Status:")
        print("=================")
        
        if not self.server_configs and not self.active_servers:
            print("No MCP servers configured")
            return
            
        print(f"Active servers: {len(self.active_servers)}/{len(self.server_configs) + 1}\n")
        
        # Print registered servers
        for server_id, config in self.server_configs.items():
            name = config.get("name", server_id)
            url = config.get("url", "unknown")
            status = "Active" if server_id in self.active_servers else "Inactive"
            print(f"- {name} ({url}): {status}")
            
        # Print local models server if not in configs
        if "local-models-mcp" not in self.server_configs:
            local_status = "Active" if "local-models-mcp" in self.active_servers else "Inactive"
            print(f"- Local Models MCP Server (http://localhost:8085): {local_status}")
    
    def shutdown_servers(self):
        """Shutdown all running server processes"""
        logger.info("Shutting down all MCP servers")
        
        for server_id, proc in self.server_processes.items():
            if proc.poll() is None:  # Process is still running
                try:
                    # Try to terminate the process group
                    os.killpg(os.getpgid(proc.pid), signal.SIGTERM)
                    logger.info(f"Sent SIGTERM to server process: {server_id}")
                    
                    # Wait up to 5 seconds for graceful termination
                    for _ in range(10):
                        if proc.poll() is not None:
                            break
                        time.sleep(0.5)
                        
                    # If still running, kill it forcefully
                    if proc.poll() is None:
                        os.killpg(os.getpgid(proc.pid), signal.SIGKILL)
                        logger.warning(f"Sent SIGKILL to server process: {server_id}")
                        
                except Exception as e:
                    logger.error(f"Error shutting down server {server_id}: {e}")

async def main():
    """Main entry point"""
    activator = MCPServerActivator()
    
    # Register signal handlers
    def signal_handler(sig, frame):
        print("\nShutting down MCP servers...")
        activator.shutdown_servers()
        sys.exit(0)
        
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Activate all servers
    await activator.activate_all_servers()
    
    # Print status
    activator.print_server_status()
    
    # Keep the main process running to keep servers alive
    try:
        print("\nMCP servers are running. Press Ctrl+C to stop.")
        while True:
            await asyncio.sleep(60)
            # Periodically check servers
            await activator.check_all_servers()
    except KeyboardInterrupt:
        print("\nShutting down...")
    finally:
        activator.shutdown_servers()

if __name__ == "__main__":
    asyncio.run(main())