"""
MCP Server Registry

This module maintains a registry of all active MCP servers and their capabilities.
It provides functions to register, unregister, and query MCP servers.
"""

import os
import json
import asyncio
import logging
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional, Set

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MCPServerRegistry:
    """Registry to track and manage MCP servers"""
    
    def __init__(self):
        self.servers = {}
        self.server_capabilities = {}
        self.last_health_check = {}
        self.active_servers = set()
        self.initialized = False
        self.health_check_interval = 60  # seconds
        self.registry_file = os.path.join(os.path.dirname(__file__), "mcp_registry.json")
        self.health_check_task = None
    
    async def initialize(self):
        """Initialize the registry"""
        if self.initialized:
            return True
            
        logger.info("Initializing MCP Server Registry")
        
        # Load existing registry if available
        self._load_registry()
        
        # Start health check loop
        if not self.health_check_task or self.health_check_task.done():
            self.health_check_task = asyncio.create_task(self._health_check_loop())
        
        self.initialized = True
        return True
        
    def _load_registry(self):
        """Load registry from file"""
        try:
            if os.path.exists(self.registry_file):
                with open(self.registry_file, 'r') as f:
                    registry_data = json.load(f)
                    
                self.servers = registry_data.get('servers', {})
                self.server_capabilities = registry_data.get('capabilities', {})
                self.last_health_check = registry_data.get('last_health_check', {})
                self.active_servers = set(registry_data.get('active_servers', []))
                
                logger.info(f"Loaded registry with {len(self.servers)} servers")
        except Exception as e:
            logger.error(f"Failed to load server registry: {str(e)}")
            # Initialize with empty values
            self.servers = {}
            self.server_capabilities = {}
            self.last_health_check = {}
            self.active_servers = set()
    
    def _save_registry(self):
        """Save registry to file"""
        try:
            registry_data = {
                'servers': self.servers,
                'capabilities': self.server_capabilities,
                'last_health_check': self.last_health_check,
                'active_servers': list(self.active_servers),
                'updated_at': datetime.now().isoformat()
            }
            
            with open(self.registry_file, 'w') as f:
                json.dump(registry_data, f, indent=2)
                
            logger.debug("Saved server registry to file")
        except Exception as e:
            logger.error(f"Failed to save server registry: {str(e)}")
    
    def register_server(self, server_id: str, name: str, url: str, capabilities: List[str] = None) -> bool:
        """
        Register a new MCP server
        
        Args:
            server_id: Unique identifier for the server
            name: Human-readable name of the server
            url: Base URL for the server
            capabilities: List of capabilities supported by the server
            
        Returns:
            Boolean indicating success
        """
        logger.info(f"Registering MCP server: {name} ({url})")
        
        if capabilities is None:
            capabilities = []
            
        # Store server details
        self.servers[server_id] = {
            'id': server_id,
            'name': name,
            'url': url,
            'registered_at': datetime.now().isoformat()
        }
        
        # Store server capabilities
        self.server_capabilities[server_id] = capabilities
        
        # Mark as active
        self.active_servers.add(server_id)
        
        # Save updated registry
        self._save_registry()
        
        logger.info(f"MCP server {name} registered with capabilities: {capabilities}")
        return True
    
    def unregister_server(self, server_id: str) -> bool:
        """
        Unregister an MCP server
        
        Args:
            server_id: Unique identifier for the server
            
        Returns:
            Boolean indicating success
        """
        if server_id not in self.servers:
            logger.warning(f"Cannot unregister unknown server: {server_id}")
            return False
            
        server_name = self.servers[server_id].get('name', server_id)
        logger.info(f"Unregistering MCP server: {server_name}")
        
        # Remove server from all collections
        if server_id in self.servers:
            del self.servers[server_id]
            
        if server_id in self.server_capabilities:
            del self.server_capabilities[server_id]
            
        if server_id in self.last_health_check:
            del self.last_health_check[server_id]
            
        if server_id in self.active_servers:
            self.active_servers.remove(server_id)
            
        # Save updated registry
        self._save_registry()
        
        logger.info(f"MCP server {server_name} unregistered")
        return True
    
    def get_server_by_id(self, server_id: str) -> Optional[Dict]:
        """Get server details by ID"""
        return self.servers.get(server_id)
    
    def get_server_capabilities(self, server_id: str) -> List[str]:
        """Get capabilities for a specific server"""
        return self.server_capabilities.get(server_id, [])
    
    def get_servers_with_capability(self, capability: str) -> List[Dict]:
        """Get all servers with a specific capability"""
        matching_servers = []
        
        for server_id, capabilities in self.server_capabilities.items():
            if capability in capabilities and server_id in self.active_servers:
                server = self.servers.get(server_id)
                if server:
                    matching_servers.append(server)
                    
        return matching_servers
    
    def get_all_servers(self) -> List[Dict]:
        """Get all registered servers"""
        return [self.servers[server_id] for server_id in self.servers]
    
    def get_active_servers(self) -> List[Dict]:
        """Get all active servers"""
        return [self.servers[server_id] for server_id in self.active_servers if server_id in self.servers]
    
    def is_server_active(self, server_id: str) -> bool:
        """Check if a server is active"""
        return server_id in self.active_servers
    
    async def check_server_health(self, server_id: str) -> bool:
        """
        Check health of a specific server
        
        Args:
            server_id: Server ID to check
            
        Returns:
            Boolean indicating if server is healthy
        """
        if server_id not in self.servers:
            logger.warning(f"Cannot check health of unknown server: {server_id}")
            return False
            
        server = self.servers[server_id]
        url = f"{server['url']}/health"
        
        try:
            response = requests.get(url, timeout=5)
            is_healthy = response.status_code == 200
            
            # Update server status
            if is_healthy:
                self.active_servers.add(server_id)
                logger.debug(f"Server {server['name']} is healthy")
            else:
                if server_id in self.active_servers:
                    self.active_servers.remove(server_id)
                logger.warning(f"Server {server['name']} health check failed with status {response.status_code}")
                
            # Record time of health check
            self.last_health_check[server_id] = {
                'timestamp': datetime.now().isoformat(),
                'status': 'healthy' if is_healthy else 'unhealthy',
                'status_code': response.status_code
            }
            
            # Save updated registry
            self._save_registry()
            
            return is_healthy
            
        except requests.exceptions.RequestException as e:
            # Connection failed - mark as inactive
            if server_id in self.active_servers:
                self.active_servers.remove(server_id)
                
            # Record failed health check
            self.last_health_check[server_id] = {
                'timestamp': datetime.now().isoformat(),
                'status': 'unreachable',
                'error': str(e)
            }
            
            # Save updated registry
            self._save_registry()
            
            logger.warning(f"Server {server['name']} is unreachable: {str(e)}")
            return False
    
    async def check_all_servers_health(self) -> Dict[str, bool]:
        """
        Check health of all registered servers
        
        Returns:
            Dictionary mapping server IDs to health status
        """
        results = {}
        
        for server_id in list(self.servers.keys()):
            is_healthy = await self.check_server_health(server_id)
            results[server_id] = is_healthy
            
        return results
    
    async def _health_check_loop(self):
        """Background task to periodically check health of all servers"""
        logger.info("Starting MCP server health check loop")
        
        while True:
            try:
                await self.check_all_servers_health()
                await asyncio.sleep(self.health_check_interval)
            except asyncio.CancelledError:
                logger.info("Health check loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in health check loop: {str(e)}")
                await asyncio.sleep(self.health_check_interval)

# Singleton registry instance
registry = MCPServerRegistry()

# For testing/debugging
if __name__ == "__main__":
    async def test_registry():
        await registry.initialize()
        
        # Register test servers
        registry.register_server(
            'test-server-1', 
            'Test Server 1', 
            'http://localhost:8080',
            ['code-completion', 'text-embedding']
        )
        
        registry.register_server(
            'test-server-2', 
            'Test Server 2', 
            'http://localhost:8081',
            ['agent-tools', 'code-execution']
        )
        
        # Check health
        health_status = await registry.check_all_servers_health()
        
        # Print active servers
        print("\nActive MCP Servers:")
        for server in registry.get_active_servers():
            print(f"- {server['name']} ({server['url']})")
            
        # Print servers by capability
        print("\nServers by capability:")
        for capability in ['code-completion', 'agent-tools']:
            servers = registry.get_servers_with_capability(capability)
            print(f"\n{capability}:")
            for server in servers:
                print(f"- {server['name']} ({server['url']})")
        
    asyncio.run(test_registry())