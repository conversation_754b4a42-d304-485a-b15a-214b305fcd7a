"""
Barber Agent
For barbershops and individual barbers (e.g., <PERSON><PERSON><PERSON><PERSON> at MIA Salon Suite, Jensen Beach Mall, Suite 410).

Main agent coordinates sub-agents for:
- Appointment Booking
- Marketing & Promotions
- Customer Engagement (e.g., reminders, loyalty)
- Analytics & Reporting

Each sub-agent is modular and can be extended.
"""

from .appointment_agent import AppointmentAgent
from .marketing_agent import MarketingAgent
from .customer_engagement_agent import CustomerEngagementAgent
from .analytics_agent import AnalyticsAgent

class BarberAgent:
    def __init__(self, barber_name, location):
        self.barber_name = barber_name
        self.location = location
        self.appointment_agent = AppointmentAgent(barber_name, location)
        self.marketing_agent = MarketingAgent(barber_name)
        self.customer_engagement_agent = CustomerEngagementAgent(barber_name)
        self.analytics_agent = AnalyticsAgent(barber_name)

    def run_all(self):
        self.appointment_agent.run()
        self.marketing_agent.run()
        self.customer_engagement_agent.run()
        self.analytics_agent.run()

# Example usage
if __name__ == "__main__":
    agent = BarberAgent("<PERSON><PERSON><PERSON><PERSON>", "MIA Salon Suite, Jensen Beach Mall, Suite 410")
    agent.run_all()
