import React from 'react';

const Sidebar = () => {
  const menuItems = [
    { icon: "📞", label: "Calls" },
    { icon: "💬", label: "Messages" },
    { icon: "📧", label: "Email" },
    { icon: "🔊", label: "Voicemail" },
    { icon: "🎙️", label: "Voice Settings" },
  ];

  return (
    <div className="w-64 h-screen bg-white border-r border-gray-200 fixed left-0 top-0 p-4">
      <div className="mb-8">
        <h1 className="text-xl font-bold text-blue-600">Comms Dashboard</h1>
      </div>
      <nav>
        {menuItems.map((item) => (
          <a
            key={item.label}
            href="#"
            className="sidebar-link mb-2"
          >
            <span>{item.icon}</span>
            <span>{item.label}</span>
          </a>
        ))}
      </nav>
    </div>
  );
};

export default Sidebar;
