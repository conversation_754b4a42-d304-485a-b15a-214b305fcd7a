#!/usr/bin/env python3
"""
Local Models MCP Server

This module provides MCP (Model Context Protocol) server functionality for local LLM models
including Qwen, Grok, and Sonnet 3.7 reasoning models.
"""

import os
import sys
import json
import logging
import asyncio
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Union, Any
from aiohttp import web
import importlib.util

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("local_mcp_server.log")
    ]
)
logger = logging.getLogger("local-mcp-server")

# Configuration paths
CONFIG_DIR = Path.home() / ".config" / "llm_models"
MODELS_DIR = Path.home() / ".local" / "models"

class LocalModelMCPServer:
    """MCP Server implementation for local LLM models"""
    
    def __init__(self, host: str = "localhost", port: int = 8085):
        self.host = host
        self.port = port
        self.app = web.Application()
        self.register_routes()
        self.models = {}
        self.model_configs = {}
        self.model_handlers = {}
        
        # Model type handlers
        self.model_type_handlers = {
            "anthropic": self._handle_anthropic_model,
            "huggingface": self._handle_huggingface_model,
        }

    def register_routes(self):
        """Register API routes"""
        self.app.router.add_get("/", self.handle_root)
        self.app.router.add_get("/health", self.handle_health)
        self.app.router.add_get("/models", self.handle_list_models)
        self.app.router.add_post("/generate", self.handle_generate)
        self.app.router.add_get("/capabilities", self.handle_capabilities)

    async def initialize(self):
        """Initialize the server and load available models"""
        logger.info("Initializing Local Models MCP Server")
        
        # Load model configurations
        await self._load_model_configs()
        
        # Initialize PyTorch for the models that need it
        self._ensure_torch_initialized()
        
        # Load models on demand to save memory
        # But make sure we have the needed imports ready
        self._ensure_dependencies()
        
        return True
        
    def _ensure_dependencies(self):
        """Ensure all required dependencies are available"""
        try:
            # Check for PyTorch
            import torch
            logger.info(f"PyTorch {torch.__version__} is available")
            
            # Check for HuggingFace Transformers
            import transformers
            logger.info(f"Transformers {transformers.__version__} is available")
            
            # Check for HuggingFace Hub
            import huggingface_hub
            logger.info(f"Hugging Face Hub {huggingface_hub.__version__} is available")
            
            return True
        except ImportError as e:
            logger.error(f"Missing dependency: {e}")
            return False
            
    def _ensure_torch_initialized(self):
        """Ensure PyTorch is initialized with appropriate settings"""
        try:
            import torch
            
            # Use MPS (Metal Performance Shaders) on Mac if available
            if torch.backends.mps.is_available():
                logger.info("Using MPS (Metal Performance Shaders) as device")
                self.device = torch.device("mps")
            # Use CUDA if available
            elif torch.cuda.is_available():
                logger.info("Using CUDA as device")
                self.device = torch.device("cuda")
            # Fallback to CPU
            else:
                logger.info("Using CPU as device")
                self.device = torch.device("cpu")
                
            return True
        except Exception as e:
            logger.error(f"Error initializing PyTorch: {e}")
            self.device = "cpu"
            return False

    async def _load_model_configs(self):
        """Load model configurations from CONFIG_DIR"""
        try:
            if not CONFIG_DIR.exists():
                logger.warning(f"Config directory not found: {CONFIG_DIR}")
                return
            
            # Find all JSON config files
            config_files = list(CONFIG_DIR.glob("*.json"))
            logger.info(f"Found {len(config_files)} model configuration files")
            
            for config_file in config_files:
                try:
                    with open(config_file, "r") as f:
                        config = json.load(f)
                    
                    model_name = config.get("model_name") or config_file.stem
                    self.model_configs[model_name] = config
                    logger.info(f"Loaded configuration for model: {model_name}")
                    
                except Exception as e:
                    logger.error(f"Error loading config file {config_file}: {e}")
        
        except Exception as e:
            logger.error(f"Error loading model configurations: {e}")
    
    async def _load_model(self, model_name: str):
        """Load a model on demand"""
        if model_name in self.models:
            logger.info(f"Model {model_name} already loaded")
            return self.models[model_name]
            
        if model_name not in self.model_configs:
            logger.error(f"No configuration found for model {model_name}")
            return None
            
        config = self.model_configs[model_name]
        model_type = config.get("model_type", "unknown")
        
        logger.info(f"Loading model {model_name} ({model_type})...")
        
        if model_type in self.model_type_handlers:
            model = await self.model_type_handlers[model_type](model_name, config)
            if model:
                self.models[model_name] = model
                logger.info(f"Successfully loaded model {model_name}")
                return model
        else:
            logger.error(f"Unsupported model type: {model_type}")
            
        return None
    
    async def _handle_anthropic_model(self, model_name: str, config: Dict):
        """Handle loading of Anthropic models like Claude and Sonnet"""
        try:
            # For Anthropic models, we'll use a wrapper around their local API
            model_path = config.get("model_path")
            
            if not Path(model_path).exists():
                logger.error(f"Model path not found: {model_path}")
                return None
            
            # Create a simple wrapper for API calls
            model_handler = {
                "type": "anthropic",
                "name": model_name,
                "path": model_path,
                "config": config,
                "generate": self._generate_with_anthropic,
            }
            
            logger.info(f"Prepared handler for Anthropic model: {model_name}")
            return model_handler
            
        except Exception as e:
            logger.error(f"Error loading Anthropic model {model_name}: {e}")
            return None
    
    async def _handle_huggingface_model(self, model_name: str, config: Dict):
        """Handle loading of HuggingFace models like Qwen and Grok"""
        try:
            import torch
            from transformers import AutoTokenizer, AutoModelForCausalLM
            
            model_path = config.get("model_path")
            hf_repo_id = config.get("hf_repo_id")
            
            # Use repo ID if local path doesn't exist
            if not Path(model_path).exists() and hf_repo_id:
                logger.info(f"Local path not found, using repo ID: {hf_repo_id}")
                load_path = hf_repo_id
            else:
                load_path = model_path
            
            # For Qwen models, we need specific config
            if "qwen" in model_name.lower():
                logger.info(f"Loading Qwen model: {model_name}")
                
                tokenizer = AutoTokenizer.from_pretrained(
                    load_path, 
                    trust_remote_code=True
                )
                
                # Load model with reduced memory usage if needed
                if torch.cuda.is_available() or torch.backends.mps.is_available():
                    model = AutoModelForCausalLM.from_pretrained(
                        load_path,
                        torch_dtype=torch.float16,  # Use half precision to save memory
                        device_map="auto",
                        trust_remote_code=True
                    )
                else:
                    model = AutoModelForCausalLM.from_pretrained(
                        load_path,
                        torch_dtype=torch.float32,
                        device_map={"": self.device},
                        trust_remote_code=True
                    )
                
                # Create a wrapper for API calls
                model_handler = {
                    "type": "huggingface",
                    "name": model_name,
                    "path": load_path,
                    "tokenizer": tokenizer,
                    "model": model,
                    "config": config,
                    "generate": self._generate_with_huggingface,
                }
                
                logger.info(f"Successfully loaded Qwen model: {model_name}")
                return model_handler
                
            # For Grok models
            elif "grok" in model_name.lower():
                logger.info(f"Loading Grok model: {model_name}")
                
                tokenizer = AutoTokenizer.from_pretrained(
                    load_path, 
                    trust_remote_code=True
                )
                
                model = AutoModelForCausalLM.from_pretrained(
                    load_path,
                    device_map="auto",
                    trust_remote_code=True
                )
                
                # Create a wrapper for API calls
                model_handler = {
                    "type": "huggingface",
                    "name": model_name,
                    "path": load_path,
                    "tokenizer": tokenizer,
                    "model": model,
                    "config": config,
                    "generate": self._generate_with_huggingface,
                }
                
                logger.info(f"Successfully loaded Grok model: {model_name}")
                return model_handler
            
            # Generic HuggingFace model handling
            else:
                logger.info(f"Loading generic HuggingFace model: {model_name}")
                
                tokenizer = AutoTokenizer.from_pretrained(load_path)
                model = AutoModelForCausalLM.from_pretrained(
                    load_path,
                    device_map="auto"
                )
                
                # Create a wrapper for API calls
                model_handler = {
                    "type": "huggingface",
                    "name": model_name,
                    "path": load_path,
                    "tokenizer": tokenizer,
                    "model": model,
                    "config": config,
                    "generate": self._generate_with_huggingface,
                }
                
                logger.info(f"Successfully loaded HuggingFace model: {model_name}")
                return model_handler
                
        except Exception as e:
            logger.error(f"Error loading HuggingFace model {model_name}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return None

    async def _generate_with_anthropic(self, model_handler: Dict, prompt: str, params: Dict):
        """Generate text using an Anthropic model"""
        try:
            # This is a simplified version. In production, you'd replace this
            # with a call to the actual Anthropic model executable.
            logger.info(f"Generating using Anthropic model: {model_handler['name']}")
            
            # For now, let's return a mock response
            model_config = model_handler['config']
            
            # If we had a real local Anthropic model, we would call it here
            # Instead, we'll return a mock response for demonstration purposes
            output = f"Response from {model_handler['name']}: This is a mock response to '{prompt[:50]}...'"
            
            return {
                "model": model_handler['name'],
                "text": output,
                "finish_reason": "completed"
            }
            
        except Exception as e:
            logger.error(f"Error generating with Anthropic model: {e}")
            return {
                "error": str(e),
                "model": model_handler['name']
            }

    async def _generate_with_huggingface(self, model_handler: Dict, prompt: str, params: Dict):
        """Generate text using a HuggingFace model"""
        try:
            logger.info(f"Generating using HuggingFace model: {model_handler['name']}")
            
            tokenizer = model_handler["tokenizer"]
            model = model_handler["model"]
            
            # Parse parameters
            max_length = params.get("max_tokens", 512)
            temperature = params.get("temperature", 0.7)
            top_p = params.get("top_p", 0.9)
            
            inputs = tokenizer(prompt, return_tensors="pt").to(model.device)
            
            # Generate text
            gen_tokens = model.generate(
                inputs.input_ids,
                max_length=min(max_length + inputs.input_ids.shape[1], model.config.max_position_embeddings),
                temperature=temperature if temperature > 0 else 1.0,
                top_p=top_p,
                do_sample=temperature > 0,  # Use sampling if temperature is not 0
                pad_token_id=tokenizer.eos_token_id
            )
            
            # Decode the generated tokens
            response_text = tokenizer.decode(gen_tokens[0][inputs.input_ids.shape[1]:], skip_special_tokens=True)
            
            return {
                "model": model_handler['name'],
                "text": response_text,
                "finish_reason": "completed"
            }
            
        except Exception as e:
            logger.error(f"Error generating with HuggingFace model: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return {
                "error": str(e),
                "model": model_handler['name']
            }

    # API Handlers
    async def handle_root(self, request):
        """Handle root endpoint"""
        return web.json_response({
            "name": "Local Models MCP Server",
            "version": "1.0.0",
            "status": "operational",
            "models_available": len(self.model_configs)
        })
    
    async def handle_health(self, request):
        """Handle health check endpoint"""
        return web.json_response({
            "status": "healthy",
            "models": len(self.model_configs),
            "loaded": len(self.models),
        })
        
    async def handle_list_models(self, request):
        """Handle listing available models"""
        models_list = []
        
        for model_name, config in self.model_configs.items():
            models_list.append({
                "id": model_name,
                "name": config.get("model_name", model_name),
                "type": config.get("model_type", "unknown"),
                "loaded": model_name in self.models,
                "capabilities": self._get_model_capabilities(model_name, config)
            })
            
        return web.json_response({
            "models": models_list
        })
        
    async def handle_capabilities(self, request):
        """Handle capabilities endpoint"""
        model_capabilities = {}
        
        for model_name, config in self.model_configs.items():
            model_capabilities[model_name] = self._get_model_capabilities(model_name, config)
            
        return web.json_response({
            "capabilities": model_capabilities
        })
    
    def _get_model_capabilities(self, model_name: str, config: Dict) -> List[str]:
        """Get capabilities for a specific model"""
        capabilities = ["text-generation"]
        model_type = config.get("model_type", "unknown")
        
        if "sonnet-3-7-reason" in model_name or "grok" in model_name:
            capabilities.extend(["reasoning", "tool-use"])
        if "qwen" in model_name.lower():
            capabilities.extend(["code-completion"])
        if "claude-3-7" in model_name:
            capabilities.extend(["tool-use"])
        
        # Add more specific capabilities based on model
        return capabilities
    
    async def handle_generate(self, request):
        """Handle text generation endpoint"""
        try:
            data = await request.json()
            
            model_name = data.get("model")
            prompt = data.get("prompt")
            params = data.get("parameters", {})
            
            if not model_name:
                return web.json_response({"error": "Model name is required"}, status=400)
            
            if not prompt:
                return web.json_response({"error": "Prompt is required"}, status=400)
                
            # Load model if not already loaded
            if model_name not in self.models:
                model_handler = await self._load_model(model_name)
                if not model_handler:
                    return web.json_response({"error": f"Failed to load model {model_name}"}, status=404)
            else:
                model_handler = self.models[model_name]
            
            # Generate text
            logger.info(f"Generating text with model {model_name}")
            response = await model_handler["generate"](model_handler, prompt, params)
            
            return web.json_response(response)
            
        except Exception as e:
            logger.error(f"Error handling generation request: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return web.json_response({"error": str(e)}, status=500)

    async def start(self):
        """Start the server"""
        await self.initialize()
        runner = web.AppRunner(self.app)
        await runner.setup()
        site = web.TCPSite(runner, self.host, self.port)
        await site.start()
        logger.info(f"Local Models MCP Server running at http://{self.host}:{self.port}/")
        
        # Keep the server running
        while True:
            await asyncio.sleep(3600)  # Sleep for an hour, then check if we should continue

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Local Models MCP Server")
    parser.add_argument("--host", default="localhost", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8085, help="Port to bind to")
    args = parser.parse_args()
    
    server = LocalModelMCPServer(host=args.host, port=args.port)
    
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(server.start())
    except KeyboardInterrupt:
        logger.info("Server stopped by keyboard interrupt")
    finally:
        loop.close()

if __name__ == "__main__":
    main()