// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.241.1/containers/python-3
{
	"name": "pentestGPT_devenv",	
	// "build": {
	// 	"dockerfile": "Dockerfile",
	// 	"context": "..",
	// 	"args": { 
	// 		// Update 'VARIANT' to pick a Python version: 3, 3.10, 3.9, 3.8, 3.7, 3.6
	// 		// Append -bullseye or -buster to pin to an OS version.
	// 		// Use -bullseye variants on local on arm64/Apple Silicon.
	// 		"VARIANT": "3.10-bullseye",
	// 		// Options
	// 		"NODE_VERSION": "lts/*"
	// 	}
	// },

	"dockerComposeFile": ["./docker-compose.yml"],
	"service": "devenv",
	// "shutdownAction": "none",  // don't shut down container when vscode is closed
	"workspaceFolder": "/workspace",

	// Configure tool-specific properties.
	"customizations": {
		// Configure properties specific to VS Code.
		"vscode": {
			// Set *default* container specific settings.json values on container create.
			"settings": { 
				"python.defaultInterpreterPath": "/usr/local/bin/python",
				"python.linting.enabled": true,
				"python.linting.pylintEnabled": true,
				"python.formatting.autopep8Path": "/usr/local/py-utils/bin/autopep8",
				"python.formatting.blackPath": "/usr/local/py-utils/bin/black",
				"python.formatting.yapfPath": "/usr/local/py-utils/bin/yapf",
				"python.linting.banditPath": "/usr/local/py-utils/bin/bandit",
				"python.linting.flake8Path": "/usr/local/py-utils/bin/flake8",
				"python.linting.mypyPath": "/usr/local/py-utils/bin/mypy",
				"python.linting.pycodestylePath": "/usr/local/py-utils/bin/pycodestyle",
				"python.linting.pydocstylePath": "/usr/local/py-utils/bin/pydocstyle",
				"python.linting.pylintPath": "/usr/local/py-utils/bin/pylint"
			},
			
			// Add the IDs of extensions you want installed when the container is created.
			"extensions": [
				"ms-python.python",
				"ms-toolsai.jupyter-renderers",
				"ms-toolsai.jupyter",
				"ms-python.vscode-pylance",
				"ms-vscode.cmake-tools",
				"leafvmaple.verilog",
				"mshr-h.VerilogHDL",
				"GitHub.copilot"
			]
		}
	},

	// Use 'forwardPorts' to make a list of ports inside the container available locally.
	// "forwardPorts": [],

	// Use 'postCreateCommand' to run commands after the container is created.
	// "postCreateCommand": "pip3 install --user -r requirements.txt",  // doing it instead in 
																		// the dockerfile to cache
																		// requirements in container

	// // Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
	// "remoteUser": "vscode"

	// "runArgs": ["--privileged"],
	"runArgs": [
		"--privileged",
		"-e", "DISPLAY=host.docker.internal:0",
		"-v", "/tmp/.X11-unix:/tmp/.X11-unix"
	]

}
