"""
Flo Faction Dashboard

This script provides a unified dashboard for managing all communications
and integrations for the Flo Faction insurance agency.
"""

import os
import sys
import json
import time
from datetime import datetime
import argparse

# Import communication modules
try:
    from twilio.rest import Client as TwilioClient
    from elevenlabs import generate as generate_speech
    import requests
    import smtplib
    import ssl
    from email.mime.text import MIMEText
    from email.mime.multipart import MI<PERSON><PERSON><PERSON>ip<PERSON>
    from email.mime.image import MIMEImage
except ImportError:
    print("Warning: Some required packages are missing. Run:")
    print("pip install twilio elevenlabs requests")

# ======================== CONFIGURATION ========================
# Client information
PAUL_EDWARDS = {
    "first_name": "<PERSON>",
    "last_name": "<PERSON>",
    "email": "<EMAIL>",
    "phone": "+***********",
    "age": 32,
    "income": 85000,
    "budget": 200,
    "retirement_focus": True,
    "health_status": "good"
}

# Twilio credentials
TWILIO_ACCOUNT_SID = os.environ.get("TWILIO_ACCOUNT_SID", "AC187c871afa232bbbc978caf33f3e25d9")
TWILIO_AUTH_TOKEN = os.environ.get("TWILIO_AUTH_TOKEN", "CqpVewwter1BEMdFIFHrN2XmUyt22wBP")
TWILIO_PHONE_NUMBER = os.environ.get("TWILIO_PHONE_NUMBER", "+***********")

# Eleven Labs credentials
ELEVEN_LABS_API_KEY = os.environ.get("ELEVEN_LABS_API_KEY", "sk_531ff39d6099a4191b57e4935654c909d6196b59d9339015")
ELEVEN_LABS_VOICE_ID = os.environ.get("ELEVEN_LABS_VOICE_ID", "21m00Tcm4TlvDq8ikWAM")

# Gmail credentials
GMAIL_EMAIL = os.environ.get("GMAIL_EMAIL", "<EMAIL>")
GMAIL_APP_PASSWORD = os.environ.get("GMAIL_APP_PASSWORD", "your_app_password_here")

# Social media credentials
FACEBOOK_PAGE_ID = os.environ.get("FACEBOOK_PAGE_ID", "your_page_id")
FACEBOOK_ACCESS_TOKEN = os.environ.get("FACEBOOK_ACCESS_TOKEN", "your_access_token")
INSTAGRAM_ACCOUNT_ID = os.environ.get("INSTAGRAM_ACCOUNT_ID", "your_instagram_account_id")

# Flo Faction information
FLO_FACTION = {
    "name": "Flo Faction Insurance",
    "website": "https://www.flofaction.com",
    "insurance_page": "https://www.flofaction.com/insurance",
    "quote_submission_form": "https://www.flofaction.com/insurancequotesubmissionform",
    "facebook": "https://www.facebook.com/flofaction",
    "instagram": "https://www.instagram.com/flofaction",
    "phone": "************",
    "email": GMAIL_EMAIL,
    "address": "123 Insurance Way, Insurance City, FL 12345",
    "logo_path": "assets/flo_faction_logo.png"
}

# ======================== COMMUNICATION FUNCTIONS ========================
def send_text_message(to_number, message):
    """Send a text message using Twilio"""
    try:
        client = TwilioClient(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
        
        # Format phone number for Twilio
        if not to_number.startswith("+"):
            to_number = "+1" + to_number
        
        message = client.messages.create(
            body=message,
            from_=TWILIO_PHONE_NUMBER,
            to=to_number
        )
        
        print(f"Text message sent with SID: {message.sid}")
        return {"success": True, "message_sid": message.sid}
    except Exception as e:
        print(f"Error sending text message: {str(e)}")
        return {"success": False, "error": str(e)}

def make_phone_call(to_number, script):
    """Make a phone call using Twilio"""
    try:
        client = TwilioClient(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
        
        # Format phone number for Twilio
        if not to_number.startswith("+"):
            to_number = "+1" + to_number
        
        # Create TwiML for the call
        twiml = f"""
        <Response>
            <Say voice="woman">
                {script}
            </Say>
            <Pause length="2"/>
            <Say voice="woman">
                This is the end of the call. Thank you.
            </Say>
        </Response>
        """
        
        call = client.calls.create(
            twiml=twiml,
            to=to_number,
            from_=TWILIO_PHONE_NUMBER
        )
        
        print(f"Call initiated with SID: {call.sid}")
        return {"success": True, "call_sid": call.sid}
    except Exception as e:
        print(f"Error making call: {str(e)}")
        return {"success": False, "error": str(e)}

def send_voicemail(to_number, script):
    """Send a voicemail using Twilio"""
    try:
        client = TwilioClient(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
        
        # Format phone number for Twilio
        if not to_number.startswith("+"):
            to_number = "+1" + to_number
        
        # Create TwiML for the voicemail
        twiml = f"""
        <Response>
            <Pause length="2"/>
            <Say voice="woman">
                {script}
            </Say>
            <Hangup/>
        </Response>
        """
        
        call = client.calls.create(
            twiml=twiml,
            to=to_number,
            from_=TWILIO_PHONE_NUMBER
        )
        
        print(f"Voicemail initiated with SID: {call.sid}")
        return {"success": True, "call_sid": call.sid}
    except Exception as e:
        print(f"Error sending voicemail: {str(e)}")
        return {"success": False, "error": str(e)}

def send_email(to_email, subject, body):
    """Send an email using Gmail"""
    try:
        message = MIMEMultipart()
        message["From"] = f"{FLO_FACTION['name']} <{GMAIL_EMAIL}>"
        message["To"] = to_email
        message["Subject"] = subject
        
        # Attach body
        message.attach(MIMEText(body, "plain"))
        
        # Create secure connection and send email
        context = ssl.create_default_context()
        with smtplib.SMTP("smtp.gmail.com", 587) as server:
            server.starttls(context=context)
            server.login(GMAIL_EMAIL, GMAIL_APP_PASSWORD)
            server.sendmail(GMAIL_EMAIL, to_email, message.as_string())
        
        print(f"Email sent to {to_email}")
        return {"success": True}
    except Exception as e:
        print(f"Error sending email: {str(e)}")
        return {"success": False, "error": str(e)}

# ======================== COMMUNICATION TEMPLATES ========================
def get_communication_templates(client_data):
    """Get communication templates for a client"""
    templates = {
        "call_script": f"""
Hello {client_data['first_name']}, this is Sandra Smith with Flo Faction Insurance. How are you doing today?

I'm reaching out because I help people like you create tax-free retirement income without the risk of the stock market. Based on your profile, I've found that you might qualify for a strategy that could provide around $5,000 per month in tax-free retirement income. Would that be something you'd be interested in learning more about?

Great! I've prepared a personalized analysis for you that shows how an Indexed Universal Life policy could help you build tax-free retirement income while also providing a death benefit of around $720,000 to protect your family.

I'd like to schedule about 30 minutes to walk you through this analysis and answer any questions you might have. Would Tuesday at 2:00 PM or Thursday at 4:00 PM work better for your schedule?

Excellent! I'll send you a calendar invitation with all the details. Before we wrap up, do you have any initial questions about how this strategy works?

I'm looking forward to our conversation. In the meantime, I'll send you a brief overview via email so you can get familiar with the concept. You can also check out our insurance page at {FLO_FACTION['insurance_page']} for more information. Thank you for your time today, {client_data['first_name']}, and have a great day!
        """,
        
        "voicemail_script": f"""
Hi {client_data['first_name']}, this is Sandra Smith with Flo Faction Insurance. I'm calling because I help people like you create tax-free retirement income without the risk of the stock market.

Based on your profile, I've found that you might qualify for a strategy that could provide significant tax-free retirement income while also protecting your family with a death benefit.

I'd love to share a personalized analysis I've prepared for you. Please give me a call back at {FLO_FACTION['phone']} when you have a moment, or visit our insurance page at {FLO_FACTION['insurance_page']} for more information.

Again, this is Sandra Smith at {FLO_FACTION['phone']}. I look forward to speaking with you soon. Thank you!
        """,
        
        "text_message": f"""
Hi {client_data['first_name']}, this is Sandra Smith with Flo Faction Insurance. I help people create tax-free retirement income without stock market risk. I've prepared a personalized analysis for you. Would you be interested in seeing this analysis? Call me at {FLO_FACTION['phone']} or submit your info at {FLO_FACTION['quote_submission_form']} for a quick quote. Thanks!
        """,
        
        "email_subject": "Creating Tax-Free Retirement Income Without Market Risk",
        
        "email_body": f"""
Dear {client_data['first_name']},

I hope this email finds you well. My name is Sandra Smith with Flo Faction Insurance, and I specialize in helping people like you create tax-free retirement income without the risk of the stock market.

Based on the information I have, I've prepared a personalized analysis that shows how you could potentially:

1. Generate approximately $5,000 per month in tax-free retirement income
2. Build cash value that grows without the risk of market losses
3. Provide a death benefit of around $720,000 to protect your family
4. Create a financial strategy that offers both protection and growth potential

Many of my clients are concerned about:
- Market volatility affecting their retirement savings
- Taxes reducing their retirement income
- Having enough money to maintain their lifestyle in retirement
- Protecting their family financially

The strategy I'd like to share with you addresses all these concerns through an Indexed Universal Life policy specifically designed for your situation.

Would you be interested in a brief 30-minute conversation to review this analysis? If so, please call me at {FLO_FACTION['phone']} or reply to this email with a good time to connect.

You can also learn more about our insurance services here:
- Insurance Information: {FLO_FACTION['insurance_page']}
- Submit Your Quote Request: {FLO_FACTION['quote_submission_form']}
- Follow us on social media:
  - Facebook: {FLO_FACTION['facebook']}
  - Instagram: {FLO_FACTION['instagram']}

Thank you for your time, and I look forward to speaking with you soon.

Best regards,
Sandra Smith
Flo Faction Insurance
{FLO_FACTION['phone']}
{FLO_FACTION['email']}
{FLO_FACTION['insurance_page']}
        """
    }
    
    return templates

# ======================== DASHBOARD FUNCTIONS ========================
def send_all_communications(client_data):
    """Send all communications to a client"""
    print("=" * 80)
    print(f"SENDING COMMUNICATIONS TO {client_data['first_name'].upper()} {client_data['last_name'].upper()}")
    print("=" * 80)
    
    # Get communication templates
    templates = get_communication_templates(client_data)
    
    results = {
        "call": None,
        "voicemail": None,
        "text": None,
        "email": None
    }
    
    # 1. Make phone call
    print("\n1. MAKING PHONE CALL")
    print("-" * 80)
    results["call"] = make_phone_call(client_data["phone"], templates["call_script"])
    
    # Wait a bit before sending the voicemail to avoid conflicts
    if results["call"]["success"]:
        print("Waiting 30 seconds before sending voicemail...")
        time.sleep(30)
    
    # 2. Send voicemail
    print("\n2. SENDING VOICEMAIL")
    print("-" * 80)
    results["voicemail"] = send_voicemail(client_data["phone"], templates["voicemail_script"])
    
    # 3. Send text message
    print("\n3. SENDING TEXT MESSAGE")
    print("-" * 80)
    results["text"] = send_text_message(client_data["phone"], templates["text_message"])
    
    # 4. Send email
    print("\n4. SENDING EMAIL")
    print("-" * 80)
    results["email"] = send_email(
        client_data["email"],
        templates["email_subject"],
        templates["email_body"]
    )
    
    # Summary
    print("\n" + "=" * 80)
    print("COMMUNICATION SUMMARY")
    print("=" * 80)
    
    for comm_type, result in results.items():
        status = "Success" if result and result.get("success", False) else "Failed"
        print(f"{comm_type.capitalize()}: {status}")
        if result and not result.get("success", False):
            print(f"  Error: {result.get('error', 'Unknown error')}")
    
    print("\nAll communications have been processed.")
    print("=" * 80)
    
    return results

def check_configuration():
    """Check if all required configuration is set"""
    missing = []
    
    if TWILIO_AUTH_TOKEN == "CqpVewwter1BEMdFIFHrN2XmUyt22wBP":
        missing.append("Twilio Auth Token")
    
    if GMAIL_APP_PASSWORD == "your_app_password_here":
        missing.append("Gmail App Password")
    
    if FACEBOOK_ACCESS_TOKEN == "your_access_token":
        missing.append("Facebook Access Token")
    
    if missing:
        print("Warning: The following configuration items are missing or using default values:")
        for item in missing:
            print(f"- {item}")
        print("\nSome features may not work correctly.")
        return False
    
    return True

def show_dashboard_menu():
    """Show the main dashboard menu"""
    print("\n" + "=" * 80)
    print("FLO FACTION INSURANCE DASHBOARD")
    print("=" * 80)
    
    print("\nAvailable Actions:")
    print("1. Send Communications to Paul Edwards")
    print("2. Check Configuration Status")
    print("3. Show Communication Templates")
    print("4. Show Integration Instructions")
    print("5. Exit")
    
    choice = input("\nEnter your choice (1-5): ")
    return choice

def show_communication_templates():
    """Show communication templates for Paul Edwards"""
    templates = get_communication_templates(PAUL_EDWARDS)
    
    print("\n" + "=" * 80)
    print("COMMUNICATION TEMPLATES FOR PAUL EDWARDS")
    print("=" * 80)
    
    print("\n1. CALL SCRIPT:")
    print("-" * 80)
    print(templates["call_script"])
    
    print("\n2. VOICEMAIL SCRIPT:")
    print("-" * 80)
    print(templates["voicemail_script"])
    
    print("\n3. TEXT MESSAGE:")
    print("-" * 80)
    print(templates["text_message"])
    
    print("\n4. EMAIL:")
    print("-" * 80)
    print(f"Subject: {templates['email_subject']}")
    print(f"Body:\n{templates['email_body']}")
    
    print("\n" + "=" * 80)

def show_integration_instructions():
    """Show integration instructions"""
    print("\n" + "=" * 80)
    print("INTEGRATION INSTRUCTIONS")
    print("=" * 80)
    
    print("""
To fully integrate all systems with the Flo Faction website and social media:

1. Twilio Integration:
   - Upgrade to a full Twilio account
   - Purchase a dedicated phone number
   - Set up webhooks for incoming calls and messages
   - Configure TwiML apps for call handling

2. Gmail Integration:
   - Generate an app password for Gmail
   - Set up email templates in Gmail
   - Configure automated email sequences
   - Set up email tracking

3. Eleven Labs Integration:
   - Select a voice for your agency
   - Generate audio files for common scripts
   - Host audio files for Twilio to access

4. Social Media Integration:
   - Connect Facebook and Instagram accounts
   - Set up Facebook Lead Ads
   - Configure Instagram Shopping
   - Implement Facebook Pixel on website

5. Wix Website Integration:
   - Add custom code for API calls
   - Set up form handlers for lead capture
   - Create client database
   - Implement dashboard for agents

For detailed instructions on each integration, see the following files:
- twilio_setup_guide.md
- gmail_setup_guide.md
- flo_faction_integration_guide.md
    """)
    
    print("\n" + "=" * 80)

# ======================== MAIN FUNCTION ========================
def main():
    """Main function for the Flo Faction Dashboard"""
    print("Welcome to the Flo Faction Insurance Dashboard!")
    print("This dashboard helps you manage communications with clients like Paul Edwards.")
    
    # Check configuration
    check_configuration()
    
    while True:
        choice = show_dashboard_menu()
        
        if choice == "1":
            # Send communications to Paul Edwards
            send_all_communications(PAUL_EDWARDS)
        elif choice == "2":
            # Check configuration status
            check_configuration()
        elif choice == "3":
            # Show communication templates
            show_communication_templates()
        elif choice == "4":
            # Show integration instructions
            show_integration_instructions()
        elif choice == "5":
            # Exit
            print("\nThank you for using the Flo Faction Insurance Dashboard!")
            break
        else:
            print("\nInvalid choice. Please enter a number between 1 and 5.")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
