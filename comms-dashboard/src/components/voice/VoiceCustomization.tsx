import React, { useState } from 'react';
import { VoiceSettings } from '../../types/voice';

const VoiceCustomization = () => {
  const [settings, setSettings] = useState<VoiceSettings>({
    stability: 0.5,
    similarity: 0.7,
    style: 0.3,
    speakerBoost: true,
    modelId: 'default',
  });

  const handleChange = (key: keyof VoiceSettings, value: number | boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="card">
      <h2 className="text-xl font-semibold mb-4">Voice Settings</h2>
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Stability</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={settings.stability}
            onChange={(e) => handleChange('stability', Number(e.target.value))}
            className="w-full"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Voice Similarity</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={settings.similarity}
            onChange={(e) => handleChange('similarity', Number(e.target.value))}
            className="w-full"
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">Style</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={settings.style}
            onChange={(e) => handleChange('style', Number(e.target.value))}
            className="w-full"
          />
        </div>
        <div className="flex items-center">
          <input
            type="checkbox"
            checked={settings.speakerBoost}
            onChange={(e) => handleChange('speakerBoost', e.target.checked)}
            className="mr-2"
          />
          <label className="text-sm font-medium">Speaker Boost</label>
        </div>
      </div>
    </div>
  );
};

export default VoiceCustomization;
