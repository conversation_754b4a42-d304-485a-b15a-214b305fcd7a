#!/usr/bin/env python3
"""
MCP Client - Connect to Model Context Protocol Servers

This module provides client functionality to connect to MCP servers
and leverage their capabilities for agent functionality.
"""

import os
import json
import logging
import asyncio
import requests
from typing import Dict, List, Any, Optional, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("MCP-Client")

class MCPClient:
    """Client for interacting with MCP servers"""
    
    def __init__(self):
        self.servers = {}
        self.active_servers = []
        self.capabilities = {}
        self.client_id = "paul-edwards-agent-system"
        self.client_name = "Paul Edwards AI Agent System"
        
    async def initialize(self) -> bool:
        """Initialize the client"""
        # Try to import registry directly first
        try:
            from mcp_server_registry import registry
            await registry.initialize()
            
            # Get all active servers from registry
            active_server_ids = registry.active_servers
            
            for server_id in active_server_ids:
                server = registry.get_server_by_id(server_id)
                if server:
                    self.servers[server_id] = server
                    self.active_servers.append(server)
                    
                    # Record capabilities
                    capabilities = registry.get_server_capabilities(server_id)
                    for capability in capabilities:
                        if capability not in self.capabilities:
                            self.capabilities[capability] = []
                        self.capabilities[capability].append(server)
            
            logger.info(f"Initialized MCP client with {len(self.active_servers)} active servers")
            logger.info(f"Available capabilities: {', '.join(self.capabilities.keys())}")
            return True
            
        except ImportError:
            logger.warning("MCP registry not available, trying direct server loading")
            
            # Try to load from config file
            try:
                config_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), "mcp_config.json")
                if os.path.exists(config_file):
                    with open(config_file, 'r') as f:
                        config = json.load(f)
                        
                    if 'servers' in config:
                        # Initialize with servers from config
                        for server in config['servers']:
                            self.servers[server['server_id']] = server
                            
                        # Now check which ones are active
                        await self._check_active_servers()
                        return True
            except Exception as e:
                logger.error(f"Failed to initialize MCP client: {str(e)}")
                
            return False
    
    async def _check_active_servers(self) -> None:
        """Check which servers are active and update capabilities"""
        self.active_servers = []
        self.capabilities = {}
        
        for server_id, server in self.servers.items():
            try:
                # Check if server is reachable
                if await self.check_server_health(server):
                    self.active_servers.append(server)
                    
                    # Record capabilities
                    for capability in server.get('capabilities', []):
                        if capability not in self.capabilities:
                            self.capabilities[capability] = []
                        self.capabilities[capability].append(server)
            except Exception as e:
                logger.warning(f"Error checking server {server_id}: {str(e)}")
        
        logger.info(f"Found {len(self.active_servers)} active servers")
        logger.info(f"Available capabilities: {', '.join(self.capabilities.keys())}")
    
    async def check_server_health(self, server: Dict) -> bool:
        """Check if a server is healthy and reachable"""
        try:
            # For public servers, use basic domain check
            if server.get('type') == 'public':
                base_url = server['url'].split('://')[1].split('/')[0]
                url = f"https://{base_url}"
            else:
                # For local servers, use health endpoint
                url = f"{server['url']}/health"
                
            response = requests.get(url, timeout=5)
            return response.status_code < 500  # Any non-server error response is acceptable
        except Exception as e:
            logger.warning(f"Health check failed for {server.get('name')}: {str(e)}")
            return False
    
    def get_servers_with_capability(self, capability: str) -> List[Dict]:
        """Get list of servers that support a specific capability"""
        return self.capabilities.get(capability, [])
        
    def get_best_server_for_capability(self, capability: str) -> Optional[Dict]:
        """Get the best server for a specific capability"""
        servers = self.get_servers_with_capability(capability)
        
        if not servers:
            logger.warning(f"No servers found with capability: {capability}")
            return None
            
        # Prioritize servers without authentication requirements
        for server in servers:
            if not server.get('auth_required', False):
                logger.info(f"Selected server {server['name']} for {capability} (no auth required)")
                return server
                
        # If all require auth, prioritize ones where we have the auth key
        for server in servers:
            env_var = server.get('auth_env_var')
            if env_var and os.environ.get(env_var):
                logger.info(f"Selected server {server['name']} for {capability} (auth available)")
                return server
                
        # Return the first server as a fallback
        logger.warning(f"Selected server {servers[0]['name']} for {capability}, but auth may be required")
        return servers[0]
    
    async def execute_capability(self, capability: str, params: Dict) -> Dict:
        """Execute a capability on the best available server"""
        server = self.get_best_server_for_capability(capability)
        if not server:
            return {
                'success': False,
                'error': f"No server available for capability: {capability}"
            }
            
        try:
            # Prepare the request
            url = f"{server['url']}/{capability}"
            
            # Add authentication if required
            headers = {'Content-Type': 'application/json'}
            if server.get('auth_required', False):
                env_var = server.get('auth_env_var')
                if env_var and os.environ.get(env_var):
                    auth_key = os.environ[env_var]
                    auth_type = server.get('auth_type', 'api_key')
                    
                    if auth_type == 'api_key':
                        headers['Authorization'] = f"Bearer {auth_key}"
                    elif auth_type == 'oauth':
                        headers['Authorization'] = f"Bearer {auth_key}"
                    else:
                        # Generic auth header
                        headers['X-API-Key'] = auth_key
                else:
                    logger.warning(f"Missing authentication for {server['name']}")
            
            # Make the request
            logger.info(f"Calling {capability} on {server['name']}")
            response = requests.post(url, json=params, headers=headers, timeout=30)
            
            # Process the response
            if response.status_code == 200:
                return {
                    'success': True,
                    'data': response.json(),
                    'server': server['name']
                }
            else:
                return {
                    'success': False,
                    'error': f"Request failed with status {response.status_code}",
                    'response': response.text,
                    'status_code': response.status_code,
                    'server': server['name']
                }
                
        except Exception as e:
            logger.error(f"Error executing {capability}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'server': server.get('name', 'unknown')
            }
    
    async def text_completion(self, prompt: str, model: str = None, max_tokens: int = 500) -> Dict:
        """Generate text completion using available servers"""
        params = {
            'prompt': prompt,
            'max_tokens': max_tokens
        }
        
        if model:
            params['model'] = model
            
        return await self.execute_capability('text-completion', params)
    
    async def code_completion(self, code: str, language: str = None, max_tokens: int = 500) -> Dict:
        """Generate code completion using available servers"""
        params = {
            'code': code,
            'max_tokens': max_tokens
        }
        
        if language:
            params['language'] = language
            
        return await self.execute_capability('code-completion', params)
    
    async def text_embedding(self, text: str, model: str = None) -> Dict:
        """Generate text embedding using available servers"""
        params = {
            'text': text
        }
        
        if model:
            params['model'] = model
            
        return await self.execute_capability('text-embedding', params)
    
    async def image_generation(self, prompt: str, size: str = "1024x1024", n: int = 1) -> Dict:
        """Generate images using available servers"""
        params = {
            'prompt': prompt,
            'size': size,
            'n': n
        }
        
        return await self.execute_capability('image-generation', params)
    
    async def audio_generation(self, text: str, voice: str = 'alloy') -> Dict:
        """Generate audio using available servers"""
        params = {
            'text': text,
            'voice': voice
        }
        
        return await self.execute_capability('audio-generation', params)
    
    async def agent_tools(self, query: str, tools: List[Dict] = None) -> Dict:
        """Execute agent tools using available servers"""
        params = {
            'query': query
        }
        
        if tools:
            params['tools'] = tools
            
        return await self.execute_capability('agent-tools', params)
    
    async def register_with_server(self, server: Dict) -> bool:
        """Register this client with an MCP server"""
        try:
            url = f"{server['url']}/register-client"
            
            # Prepare registration data
            registration_data = {
                'client_id': self.client_id,
                'client_name': self.client_name,
                'capabilities_requested': server.get('capabilities', [])
            }
            
            # Add authentication if required
            headers = {'Content-Type': 'application/json'}
            if server.get('auth_required', False):
                env_var = server.get('auth_env_var')
                if env_var and os.environ.get(env_var):
                    headers['Authorization'] = f"Bearer {os.environ[env_var]}"
            
            # Make the request
            logger.info(f"Registering with {server['name']}")
            response = requests.post(url, json=registration_data, headers=headers, timeout=10)
            
            # Process the response
            if response.status_code == 200:
                logger.info(f"Successfully registered with {server['name']}")
                return True
            else:
                logger.warning(f"Failed to register with {server['name']}: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Error registering with {server.get('name', 'unknown')}: {str(e)}")
            return False


# Create a global client instance
client = MCPClient()

async def register_with_servers(servers: List[Dict]) -> bool:
    """Register with multiple servers"""
    if not servers:
        logger.warning("No servers provided for registration")
        return False
        
    success_count = 0
    
    for server in servers:
        if await client.register_with_server(server):
            success_count += 1
            
    return success_count > 0

async def initialize() -> bool:
    """Initialize the global client instance"""
    return await client.initialize()

# Simple test function
async def test_capabilities():
    """Test available capabilities"""
    await initialize()
    
    results = {}
    
    # Test text completion if available
    if 'text-completion' in client.capabilities:
        results['text-completion'] = await client.text_completion("Hello, my name is Paul Edwards and I am")
        
    # Test code completion if available
    if 'code-completion' in client.capabilities:
        results['code-completion'] = await client.code_completion("def fibonacci(n):\n    ")
        
    # Test text embedding if available
    if 'text-embedding' in client.capabilities:
        results['text-embedding'] = await client.text_embedding("Convert this text to an embedding")
        
    # Print test results
    for capability, result in results.items():
        success = result.get('success', False)
        server = result.get('server', 'unknown')
        
        if success:
            logger.info(f"✓ {capability} test succeeded using {server}")
        else:
            error = result.get('error', 'Unknown error')
            logger.warning(f"✗ {capability} test failed using {server}: {error}")
            
    return len(results) > 0

if __name__ == "__main__":
    """Run a simple test if executed directly"""
    logging.info("Testing MCP client capabilities")
    asyncio.run(test_capabilities())