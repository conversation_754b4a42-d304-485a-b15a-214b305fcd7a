on:
  pull_request:
    types: [opened, synchronize, reopened]
    paths:
      - 'packages/common/**'
      - 'packages/agent-tars/**'
      - '.github/workflows/e2e-agent-tars.yml'
      - 'apps/agent-tars/**'
  push:
    branches:
      - 'main'

name: Agent-TARS E2E Test
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-test
  cancel-in-progress: true
env:
  CI: e2e
  NODE_OPTIONS: --max-old-space-size=8192
  HUSKY: 0

permissions:
  id-token: write
  contents: read
  attestations: read

jobs:
  e2e:
    name: E2E
    strategy:
      fail-fast: false
      matrix:
        os: [macos-latest, macos-13]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2

      - name: Cache turbo
        uses: actions/cache@d4323d4df104b026a6aa633fdb11d772146be0bf
        with:
          path: .turbo
          key: ${{ runner.os }}-turbo-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-turbo-

      - name: Install pnpm
        run: npm install -g pnpm@9
      - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a # v4.2.0
        with:
          node-version: 20
          cache: 'pnpm'
      - name: Install Python setuptools
        if: matrix.os == 'macos-latest'
        run: brew install python-setuptools
      - name: Install appdmg
        if: matrix.os == 'macos-latest'
        run: npm install -g appdmg
      - name: Install dependencies
        run: pnpm install
      - name: Run e2e
        run: npm exec turbo run agent-tars-app#test:e2e
