import React, { useState } from 'react';
import ChannelSelector from './ChannelSelector';
import { MessageChannel } from '../../types/message';

const MessageComposer = () => {
  const [channel, setChannel] = useState<MessageChannel>('text');
  const [message, setMessage] = useState('');
  const [recipient, setRecipient] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle message submission
    console.log({ channel, message, recipient });
  };

  return (
    <div className="card">
      <h2 className="text-xl font-semibold mb-4">New Message</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <ChannelSelector
          selectedChannel={channel}
          onChannelChange={setChannel}
        />
        <input
          type="text"
          placeholder="Recipient"
          value={recipient}
          onChange={(e) => setRecipient(e.target.value)}
          className="input-field"
        />
        <textarea
          placeholder="Type your message..."
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          className="input-field min-h-[120px]"
        />
        <button type="submit" className="btn-primary">
          Send Message
        </button>
      </form>
    </div>
  );
};

export default MessageComposer;
